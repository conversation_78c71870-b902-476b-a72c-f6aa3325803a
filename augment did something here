V43356K3KM:deliveries radhvi3$ python3 -c "
> import itertools
> 
> # Define locations
> locations = [
>     'Start/End',
>     'Hollywoodrath Drive',
>     'The Gallops',
>     'Phoenix Park',
>     'Hollywoodrath Park',
>     'Cherry Avenue'
> ]
> 
> # Distance matrix (in meters)
> # [Start/End, Hollywoodrath Dr, The Gallops, Phoenix Park, Hollywoodrath Park, Cherry Ave]
> distances = [
>     [0, 18358, 15388, 11169, 18566, 11088],  # From Start/End
>     [18837, 0, 8639, 12030, 722, 8687],      # From Hollywoodrath Dr
>     [15507, 7659, 0, 10486, 7345, 6432],     # From The Gallops
>     [10134, 9821, 9670, 0, 9201, 5839],      # From Phoenix Park
>     [16730, 722, 8318, 11709, 0, 9034],      # From Hollywoodrath Park
>     [11842, 8581, 7074, 5965, 8788, 0]       # From Cherry Ave
> ]
> 
> # Time matrix (in seconds)
> # [Start/End, Hollywoodrath Dr, The Gallops, Phoenix Park, Hollywoodrath Park, Cherry Ave]
> times = [
>     [0, 1235, 1144, 1047, 1261, 1126],       # From Start/End
>     [1227, 0, 791, 930, 152, 1025],          # From Hollywoodrath Dr
>     [1152, 770, 0, 828, 760, 810],           # From The Gallops
>     [846, 923, 739, 0, 941, 696],            # From Phoenix Park
>     [1275, 159, 812, 951, 0, 1030],          # From Hollywoodrath Park
>     [1143, 1015, 801, 777, 1041, 0]          # From Cherry Ave
> ]
> 
> # Function to calculate total distance of a route
> def calculate_route_distance(route):
>     total_distance = 0
>     for i in range(len(route) - 1):
>         total_distance += distances[route[i]][route[i+1]]
>     return total_distance
> 
> # Function to calculate total time of a route
> def calculate_route_time(route):
>     total_time = 0
>     for i in range(len(route) - 1):
>         total_time += times[route[i]][route[i+1]]
>     return total_time
> 
> # Generate all possible routes (permutations of locations 1-5)
> # Start and end at location 0 (Start/End point)
> best_distance = float('inf')
> best_route = None
> best_time = float('inf')
> 
> for perm in itertools.permutations(range(1, 6)):
>     route = [0] + list(perm) + [0]  # Start and end at location 0
>     distance = calculate_route_distance(route)
>     time = calculate_route_time(route)
>     
>     if distance < best_distance:
>         best_distance = distance
>         best_route = route
>         best_time = time
> 
> # Print the best route
> print('Optimal Route:')
> for i in range(len(best_route) - 1):
>     from_loc = locations[best_route[i]]
>     to_loc = locations[best_route[i+1]]
>     dist = distances[best_route[i]][best_route[i+1]]
>     time_sec = times[best_route[i]][best_route[i+1]]
>     time_min = time_sec // 60
>     print(f'{from_loc} → {to_loc}: {dist/1000:.1f} km, {time_min} mins')
> 
> print(f'\\nTotal Distance: {best_distance/1000:.1f} km')
> print(f'Total Time: {best_time//60} mins')
> 
> # Print the route indices for reference
> print(f'\\nRoute indices: {best_route}')
> "
Optimal Route:
Start/End → Phoenix Park: 11.2 km, 17 mins
Phoenix Park → Hollywoodrath Park: 9.2 km, 15 mins
Hollywoodrath Park → Hollywoodrath Drive: 0.7 km, 2 mins
Hollywoodrath Drive → The Gallops: 8.6 km, 13 mins
The Gallops → Cherry Avenue: 6.4 km, 13 mins
Cherry Avenue → Start/End: 11.8 km, 19 mins

Total Distance: 48.0 km
Total Time: 81 mins

Route indices: [0, 3, 4, 1, 2, 5, 0]