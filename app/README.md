# Customer Location Map

A simple web application that displays customer locations on an interactive map using data from your existing `db/customers.csv` file.

## Features

- **Interactive Map**: Uses Leaflet.js with OpenStreetMap tiles (completely free)
- **Customer Markers**: Displays a marker for each customer location
- **Popup Information**: Click markers to see customer details (name, address, phone, EirCode)
- **Auto-fit View**: Automatically adjusts map view to show all customer locations
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: Graceful handling of missing or invalid data

## File Structure

```
app/
├── index.html          # Main HTML file
├── css/
│   └── styles.css     # Custom styles
├── js/
│   └── main.js        # Main application logic
└── README.md          # This documentation
```

## How to Use

1. **Open the Application**:
   - Open `app/index.html` in a web browser
   - No web server required for file upload functionality

2. **Load Customer Data**:
   - Click the "Choose CSV File" button
   - Select a CSV file from your local file system
   - The selected file name will be displayed
   - Markers will appear on the map for each customer with valid coordinates

3. **Interact with the Map**:
   - **Zoom**: Use mouse wheel or zoom controls
   - **Pan**: Click and drag to move around
   - **View Details**: Click any marker to see customer information

## CSV Data Format

The application expects your existing CSV format:

```csv
Name,EirCode,Address,lat,long,Phone
John Doe,D16E2V9,123 Main Street Dublin,53.2802308,-6.3128555,+353 89 123 4567
```

**Required Fields**:
- `lat`: Latitude coordinate (decimal degrees)
- `long`: Longitude coordinate (decimal degrees)

**Optional Fields**:
- `Name`: Customer name
- `EirCode`: Irish postal code
- `Address`: Full address
- `Phone`: Contact phone number

## Technical Details

### Dependencies (loaded via CDN)
- **Leaflet.js 1.9.4**: Interactive map library
- **Papa Parse 5.4.1**: CSV parsing library

### Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)

### No API Keys Required
- Uses OpenStreetMap tiles (free and open source)
- No Google Maps API key needed
- No usage limits or costs

## Troubleshooting

### Common Issues

1. **No markers appear**:
   - Check that your CSV file has valid lat/long coordinates
   - Ensure the CSV has the correct column headers (Name, EirCode, Address, lat, long, Phone)
   - Check browser console for error messages

2. **File upload fails**:
   - Ensure you're selecting a valid CSV file
   - Check that the file isn't corrupted or empty
   - Try with a smaller CSV file to test

3. **Markers in wrong locations**:
   - Verify latitude and longitude values in your CSV
   - Ensure coordinates are in decimal degrees format (not degrees/minutes/seconds)

### Running with a Local Server

For best results, serve the application using a local web server:

```bash
# Using Python 3
cd /path/to/your/workspace
python -m http.server 8000

# Using Node.js (if you have npx)
cd /path/to/your/workspace
npx serve .

# Using PHP
cd /path/to/your/workspace
php -S localhost:8000
```

Then open `http://localhost:8000/app/` in your browser.

## Future Enhancements

Potential improvements you could add:

- **Search functionality**: Find specific customers
- **Filtering**: Show/hide customers based on criteria
- **Clustering**: Group nearby markers when zoomed out
- **Export features**: Save filtered data or map views
- **Custom markers**: Different icons for different customer types
- **Routing**: Calculate routes between customer locations

## License

This is a simple utility application. Feel free to modify and use as needed.
