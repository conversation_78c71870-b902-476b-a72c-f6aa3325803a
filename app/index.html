<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Location Map</title>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Customer Location Map</h1>
            <div class="controls">
                <div class="file-upload-section">
                    <input type="file" id="csvFileInput" accept=".csv" style="display: none;">
                    <button id="loadData" class="btn btn-primary">Choose CSV File</button>
                    <span id="fileName" class="file-name"></span>
                </div>
                <div class="info-panel">
                    <span id="customerCount">0 customers loaded</span>
                </div>
            </div>
        </header>

        <main>
            <div id="map"></div>
        </main>

        <div id="loading" class="loading hidden">
            <div class="spinner"></div>
            <p>Loading customer data...</p>
        </div>

        <div id="error" class="error hidden">
            <p id="errorMessage"></p>
            <button id="closeError" class="btn btn-secondary">Close</button>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Papa Parse for CSV parsing -->
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/main.js"></script>
</body>
</html>
