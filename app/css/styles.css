/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header styles */
header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
}

header h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.file-upload-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-name {
    font-size: 0.9rem;
    color: #27ae60;
    font-style: italic;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.info-panel {
    background: #e8f4fd;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    color: #2980b9;
}

/* Button styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

/* Main content */
main {
    flex: 1;
    position: relative;
}

#map {
    height: 100%;
    width: 100%;
}

/* Loading overlay */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error overlay */
.error {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 2000;
    max-width: 400px;
    text-align: center;
}

.error p {
    margin-bottom: 1rem;
    color: #e74c3c;
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Custom marker popup styles */
.leaflet-popup-content {
    margin: 8px 12px;
    line-height: 1.4;
}

.popup-content h3 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.popup-content p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.popup-content .address {
    color: #7f8c8d;
    font-style: italic;
}

.popup-content .phone {
    color: #27ae60;
    font-weight: 500;
}

.popup-content .eircode {
    background: #ecf0f1;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.8rem;
}

/* Responsive design */
@media (max-width: 768px) {
    header {
        padding: 1rem;
    }

    header h1 {
        font-size: 1.3rem;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .info-panel {
        text-align: center;
    }
}
