// Customer Location Map Application
class CustomerMap {
    constructor() {
        this.map = null;
        this.markers = [];
        this.customerData = [];

        this.init();
    }

    init() {
        this.initializeMap();
        this.bindEvents();
        this.showLoading(false);
    }

    initializeMap() {
        // Initialize map centered on Dublin area (based on your customer data)
        this.map = L.map('map').setView([53.3498, -6.2603], 11);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19
        }).addTo(this.map);

        // Add scale control
        L.control.scale().addTo(this.map);
    }

    bindEvents() {
        const loadButton = document.getElementById('loadData');
        const fileInput = document.getElementById('csvFileInput');
        const closeErrorButton = document.getElementById('closeError');

        loadButton.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (event) => this.handleFileSelect(event));
        closeErrorButton.addEventListener('click', () => this.hideError());
    }

    handleFileSelect(event) {
        const file = event.target.files[0];

        if (!file) {
            return;
        }

        // Validate file type
        if (!file.name.toLowerCase().endsWith('.csv')) {
            this.showError('Please select a CSV file.');
            return;
        }

        // Update file name display
        this.updateFileName(file.name);

        // Read and process the file
        this.readAndProcessFile(file);
    }

    updateFileName(fileName) {
        const fileNameElement = document.getElementById('fileName');
        fileNameElement.textContent = fileName;
    }

    readAndProcessFile(file) {
        try {
            this.showLoading(true);

            const reader = new FileReader();

            reader.onload = (event) => {
                try {
                    const csvText = event.target.result;
                    this.parseAndDisplayCustomers(csvText);
                } catch (error) {
                    console.error('Error processing file:', error);
                    this.showError(`Error processing file: ${error.message}`);
                } finally {
                    this.showLoading(false);
                }
            };

            reader.onerror = () => {
                this.showLoading(false);
                this.showError('Error reading file. Please try again.');
            };

            reader.readAsText(file);

        } catch (error) {
            console.error('Error reading file:', error);
            this.showError(`Error reading file: ${error.message}`);
            this.showLoading(false);
        }
    }

    parseAndDisplayCustomers(csvText) {
        Papa.parse(csvText, {
            header: true,
            skipEmptyLines: true,
            complete: (results) => {
                if (results.errors.length > 0) {
                    console.warn('CSV parsing warnings:', results.errors);
                }

                this.customerData = results.data.filter(row =>
                    row.lat && row.long &&
                    !isNaN(parseFloat(row.lat)) &&
                    !isNaN(parseFloat(row.long))
                );

                this.displayCustomersOnMap();
                this.updateCustomerCount();
                this.fitMapToMarkers();
            },
            error: (error) => {
                this.showError(`Error parsing CSV: ${error.message}`);
            }
        });
    }

    displayCustomersOnMap() {
        // Clear existing markers
        this.clearMarkers();

        this.customerData.forEach((customer) => {
            const lat = parseFloat(customer.lat);
            const lng = parseFloat(customer.long);

            if (isNaN(lat) || isNaN(lng)) {
                console.warn(`Invalid coordinates for customer: ${customer.Name}`);
                return;
            }

            // Create marker
            const marker = L.marker([lat, lng]).addTo(this.map);

            // Create popup content
            const popupContent = this.createPopupContent(customer);
            marker.bindPopup(popupContent);

            // Store marker reference
            this.markers.push(marker);
        });
    }

    createPopupContent(customer) {
        const name = customer.Name || 'Unknown';
        const address = customer.Address || 'No address provided';
        const phone = customer.Phone || 'No phone provided';
        const eircode = customer.EirCode || 'No EirCode';

        return `
            <div class="popup-content">
                <h3>${this.escapeHtml(name)}</h3>
                <p class="address">${this.escapeHtml(address)}</p>
                ${eircode !== 'No EirCode' ? `<p class="eircode">${this.escapeHtml(eircode)}</p>` : ''}
                <p class="phone">${this.escapeHtml(phone)}</p>
            </div>
        `;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    clearMarkers() {
        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.markers = [];
    }

    fitMapToMarkers() {
        if (this.markers.length === 0) return;

        if (this.markers.length === 1) {
            // If only one marker, center on it
            const marker = this.markers[0];
            this.map.setView(marker.getLatLng(), 15);
        } else {
            // If multiple markers, fit bounds to show all
            const group = new L.featureGroup(this.markers);
            this.map.fitBounds(group.getBounds().pad(0.1));
        }
    }

    updateCustomerCount() {
        const countElement = document.getElementById('customerCount');
        const count = this.customerData.length;
        countElement.textContent = `${count} customer${count !== 1 ? 's' : ''} loaded`;
    }

    showLoading(show) {
        const loadingElement = document.getElementById('loading');
        if (show) {
            loadingElement.classList.remove('hidden');
        } else {
            loadingElement.classList.add('hidden');
        }
    }

    showError(message) {
        const errorElement = document.getElementById('error');
        const errorMessageElement = document.getElementById('errorMessage');

        errorMessageElement.textContent = message;
        errorElement.classList.remove('hidden');
    }

    hideError() {
        const errorElement = document.getElementById('error');
        errorElement.classList.add('hidden');
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CustomerMap();
});
